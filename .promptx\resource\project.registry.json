{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-01T16:09:02.698Z", "updatedAt": "2025-08-01T16:09:02.753Z", "resourceCount": 23}, "resources": [{"id": "ai-model-integration-expert", "source": "project", "protocol": "role", "name": "Ai Model Integration Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-model-integration-expert/ai-model-integration-expert.role.md", "metadata": {"createdAt": "2025-08-01T16:09:02.701Z", "updatedAt": "2025-08-01T16:09:02.701Z", "scannedAt": "2025-08-01T16:09:02.701Z", "path": "role/ai-model-integration-expert/ai-model-integration-expert.role.md"}}, {"id": "ai-integration-workflow", "source": "project", "protocol": "execution", "name": "Ai Integration Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-model-integration-expert/execution/ai-integration-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T16:09:02.703Z", "updatedAt": "2025-08-01T16:09:02.703Z", "scannedAt": "2025-08-01T16:09:02.703Z", "path": "role/ai-model-integration-expert/execution/ai-integration-workflow.execution.md"}}, {"id": "ai-integration-thinking", "source": "project", "protocol": "thought", "name": "Ai Integration Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-model-integration-expert/thought/ai-integration-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T16:09:02.704Z", "updatedAt": "2025-08-01T16:09:02.704Z", "scannedAt": "2025-08-01T16:09:02.704Z", "path": "role/ai-model-integration-expert/thought/ai-integration-thinking.thought.md"}}, {"id": "doc-writer", "source": "project", "protocol": "role", "name": "Doc Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/doc-writer/doc-writer.role.md", "metadata": {"createdAt": "2025-08-01T16:09:02.705Z", "updatedAt": "2025-08-01T16:09:02.705Z", "scannedAt": "2025-08-01T16:09:02.705Z", "path": "role/doc-writer/doc-writer.role.md"}}, {"id": "document-standards", "source": "project", "protocol": "execution", "name": "Document Standards 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/doc-writer/execution/document-standards.execution.md", "metadata": {"createdAt": "2025-08-01T16:09:02.707Z", "updatedAt": "2025-08-01T16:09:02.707Z", "scannedAt": "2025-08-01T16:09:02.707Z", "path": "role/doc-writer/execution/document-standards.execution.md"}}, {"id": "documentation-workflow", "source": "project", "protocol": "execution", "name": "Documentation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/doc-writer/execution/documentation-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T16:09:02.708Z", "updatedAt": "2025-08-01T16:09:02.708Z", "scannedAt": "2025-08-01T16:09:02.708Z", "path": "role/doc-writer/execution/documentation-workflow.execution.md"}}, {"id": "document-types", "source": "project", "protocol": "thought", "name": "Document Types 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/doc-writer/thought/document-types.thought.md", "metadata": {"createdAt": "2025-08-01T16:09:02.712Z", "updatedAt": "2025-08-01T16:09:02.712Z", "scannedAt": "2025-08-01T16:09:02.712Z", "path": "role/doc-writer/thought/document-types.thought.md"}}, {"id": "documentation-thinking", "source": "project", "protocol": "thought", "name": "Documentation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/doc-writer/thought/documentation-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T16:09:02.713Z", "updatedAt": "2025-08-01T16:09:02.713Z", "scannedAt": "2025-08-01T16:09:02.713Z", "path": "role/doc-writer/thought/documentation-thinking.thought.md"}}, {"id": "prompt-engineering-workflow", "source": "project", "protocol": "execution", "name": "Prompt Engineering Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/prompt-engineer/execution/prompt-engineering-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T16:09:02.715Z", "updatedAt": "2025-08-01T16:09:02.715Z", "scannedAt": "2025-08-01T16:09:02.715Z", "path": "role/prompt-engineer/execution/prompt-engineering-workflow.execution.md"}}, {"id": "prompt-engineer", "source": "project", "protocol": "role", "name": "Prompt Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/prompt-engineer/prompt-engineer.role.md", "metadata": {"createdAt": "2025-08-01T16:09:02.733Z", "updatedAt": "2025-08-01T16:09:02.733Z", "scannedAt": "2025-08-01T16:09:02.733Z", "path": "role/prompt-engineer/prompt-engineer.role.md"}}, {"id": "prompt-engineering-thinking", "source": "project", "protocol": "thought", "name": "Prompt Engineering Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/prompt-engineer/thought/prompt-engineering-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T16:09:02.734Z", "updatedAt": "2025-08-01T16:09:02.734Z", "scannedAt": "2025-08-01T16:09:02.734Z", "path": "role/prompt-engineer/thought/prompt-engineering-thinking.thought.md"}}, {"id": "desktop-development-workflow", "source": "project", "protocol": "execution", "name": "Desktop Development Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/pyside6-desktop-developer/execution/desktop-development-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T16:09:02.736Z", "updatedAt": "2025-08-01T16:09:02.736Z", "scannedAt": "2025-08-01T16:09:02.736Z", "path": "role/pyside6-desktop-developer/execution/desktop-development-workflow.execution.md"}}, {"id": "pyside6-desktop-developer", "source": "project", "protocol": "role", "name": "Pyside6 Desktop Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/pyside6-desktop-developer/pyside6-desktop-developer.role.md", "metadata": {"createdAt": "2025-08-01T16:09:02.737Z", "updatedAt": "2025-08-01T16:09:02.737Z", "scannedAt": "2025-08-01T16:09:02.737Z", "path": "role/pyside6-desktop-developer/pyside6-desktop-developer.role.md"}}, {"id": "desktop-development-thinking", "source": "project", "protocol": "thought", "name": "Desktop Development Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/pyside6-desktop-developer/thought/desktop-development-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T16:09:02.739Z", "updatedAt": "2025-08-01T16:09:02.739Z", "scannedAt": "2025-08-01T16:09:02.739Z", "path": "role/pyside6-desktop-developer/thought/desktop-development-thinking.thought.md"}}, {"id": "project-coordination", "source": "project", "protocol": "execution", "name": "Project Coordination 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/project-coordination.execution.md", "metadata": {"createdAt": "2025-08-01T16:09:02.741Z", "updatedAt": "2025-08-01T16:09:02.741Z", "scannedAt": "2025-08-01T16:09:02.741Z", "path": "role/system-director/execution/project-coordination.execution.md"}}, {"id": "system-director", "source": "project", "protocol": "role", "name": "System Director 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/system-director/system-director.role.md", "metadata": {"createdAt": "2025-08-01T16:09:02.741Z", "updatedAt": "2025-08-01T16:09:02.741Z", "scannedAt": "2025-08-01T16:09:02.741Z", "path": "role/system-director/system-director.role.md"}}, {"id": "strategic-thinking", "source": "project", "protocol": "thought", "name": "Strategic Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/strategic-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T16:09:02.744Z", "updatedAt": "2025-08-01T16:09:02.744Z", "scannedAt": "2025-08-01T16:09:02.744Z", "path": "role/system-director/thought/strategic-thinking.thought.md"}}, {"id": "ui-ux-design-workflow", "source": "project", "protocol": "execution", "name": "Ui Ux Design Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ui-ux-designer/execution/ui-ux-design-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T16:09:02.746Z", "updatedAt": "2025-08-01T16:09:02.746Z", "scannedAt": "2025-08-01T16:09:02.746Z", "path": "role/ui-ux-designer/execution/ui-ux-design-workflow.execution.md"}}, {"id": "design-thinking", "source": "project", "protocol": "thought", "name": "Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ui-ux-designer/thought/design-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T16:09:02.747Z", "updatedAt": "2025-08-01T16:09:02.747Z", "scannedAt": "2025-08-01T16:09:02.747Z", "path": "role/ui-ux-designer/thought/design-thinking.thought.md"}}, {"id": "ui-ux-designer", "source": "project", "protocol": "role", "name": "Ui Ux Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ui-ux-designer/ui-ux-designer.role.md", "metadata": {"createdAt": "2025-08-01T16:09:02.748Z", "updatedAt": "2025-08-01T16:09:02.748Z", "scannedAt": "2025-08-01T16:09:02.748Z", "path": "role/ui-ux-designer/ui-ux-designer.role.md"}}, {"id": "novel-creation-workflow", "source": "project", "protocol": "execution", "name": "Novel Creation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/web-novel-expert/execution/novel-creation-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T16:09:02.750Z", "updatedAt": "2025-08-01T16:09:02.750Z", "scannedAt": "2025-08-01T16:09:02.750Z", "path": "role/web-novel-expert/execution/novel-creation-workflow.execution.md"}}, {"id": "creative-writing-thinking", "source": "project", "protocol": "thought", "name": "Creative Writing Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/web-novel-expert/thought/creative-writing-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T16:09:02.751Z", "updatedAt": "2025-08-01T16:09:02.751Z", "scannedAt": "2025-08-01T16:09:02.751Z", "path": "role/web-novel-expert/thought/creative-writing-thinking.thought.md"}}, {"id": "web-novel-expert", "source": "project", "protocol": "role", "name": "Web Novel Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/web-novel-expert/web-novel-expert.role.md", "metadata": {"createdAt": "2025-08-01T16:09:02.752Z", "updatedAt": "2025-08-01T16:09:02.752Z", "scannedAt": "2025-08-01T16:09:02.752Z", "path": "role/web-novel-expert/web-novel-expert.role.md"}}], "stats": {"totalResources": 23, "byProtocol": {"role": 7, "execution": 8, "thought": 8}, "bySource": {"project": 23}}}