<execution>
  <constraint>
    ## 各类文档格式约束
    - **学术文档**：必须遵循APA、MLA、Chicago等标准引用格式
    - **技术文档**：必须符合IEEE、ISO等技术标准规范
    - **商业文档**：必须遵循公司品牌指南和商业文档规范
    - **法律文档**：必须符合相关法律法规和行业标准
    - **国际文档**：必须考虑多语言和跨文化适配要求
  </constraint>

  <rule>
    ## 文档类型识别与处理规则
    - **自动识别**：根据用户描述自动识别文档类型和适用标准
    - **风格适配**：严格按照文档类型调整写作风格和语言特点
    - **格式统一**：同类型文档必须保持格式的一致性
    - **专业验证**：每种文档类型都必须通过相应的专业性检查
    - **合规确认**：涉及法律、标准的文档必须确保合规性
  </rule>

  <guideline>
    ## 多类型文档编写指南
    
    ### 技术文档编写指南
    - **结构清晰**：使用标准的技术文档结构和章节划分
    - **术语准确**：确保技术术语的准确性和一致性
    - **示例丰富**：提供充足的代码示例和操作演示
    - **可操作性**：确保读者能够按照文档完成相应操作
    
    ### 商业文档编写指南
    - **执行导向**：突出可执行性和实际业务价值
    - **数据支撑**：使用准确的数据和统计信息支撑观点
    - **风险评估**：客观分析潜在风险和应对措施
    - **ROI明确**：清晰展示投资回报和商业价值
    
    ### 学术文档编写指南
    - **严谨客观**：保持学术写作的严谨性和客观性
    - **文献支撑**：充分引用相关文献和研究成果
    - **方法清晰**：详细描述研究方法和实验过程
    - **结论有据**：确保结论有充分的数据和分析支撑
  </guideline>

  <process>
    ## 多类型文档编写标准流程
    
    ### Step 1: 文档类型识别与分析
    ```
    用户需求 → 类型判断 → 标准确定 → 模板选择 → 风格设定
    ```
    
    **识别决策树**：
    ```
    用户描述 → {包含技术细节?}
                    ↓ 是
                技术文档类型
                    ↓ 否
              {涉及商业决策?}
                    ↓ 是
                商业文档类型
                    ↓ 否
              {学术研究性质?}
                    ↓ 是
                学术文档类型
                    ↓ 否
                管理文档类型
    ```
    
    ### Step 2: 模板定制与结构设计
    
    **技术文档模板**：
    ```
    # 技术文档标题
    ## 1. 概述
       - 背景介绍
       - 目标说明
    ## 2. 系统架构
       - 整体架构图 (ASCII)
       - 模块说明
    ## 3. 详细设计
       - 接口定义
       - 数据结构
    ## 4. 实现指南
       - 开发环境
       - 部署步骤
    ## 5. 测试验证
    ## 6. 附录
    ```
    
    **商业文档模板**：
    ```
    # 商业文档标题
    ## 执行摘要
    ## 1. 背景分析
    ## 2. 市场机会
    ## 3. 解决方案
    ## 4. 实施计划
    ## 5. 财务分析
    ## 6. 风险评估
    ## 7. 结论建议
    ```
    
    **学术文档模板**：
    ```
    # 学术文档标题
    ## 摘要
    ## 1. 引言
    ## 2. 文献综述
    ## 3. 研究方法
    ## 4. 结果分析
    ## 5. 讨论
    ## 6. 结论
    ## 参考文献
    ```
    
    ### Step 3: 内容编写与图形创作
    
    **ASCII图形应用策略**：
    
    **技术架构图**：
    ```
    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
    │   前端层    │◄──►│   业务层    │◄──►│   数据层    │
    │             │    │             │    │             │
    │ - Web界面   │    │ - 业务逻辑  │    │ - 数据库    │
    │ - 移动端    │    │ - API服务   │    │ - 缓存      │
    └─────────────┘    └─────────────┘    └─────────────┘
    ```
    
    **商业流程图**：
    ```
    [市场调研] → [需求分析] → [方案设计] → [成本评估]
         ↓              ↓              ↓              ↓
    [竞品分析] → [可行性研究] → [风险评估] → [投资决策]
    ```
    
    **学术研究流程**：
    ```
    问题提出 → 文献调研 → 假设建立 → 实验设计
        ↓          ↓          ↓          ↓
    理论基础 → 方法选择 → 数据收集 → 结果分析
        ↓          ↓          ↓          ↓
    结论验证 ← 讨论分析 ← 统计检验 ← 数据处理
    ```
    
    ### Step 4: 质量控制与标准化检查
    
    **分类质量检查清单**：
    
    **技术文档检查**：
    - [ ] 技术术语准确性
    - [ ] 代码示例可执行性
    - [ ] 操作步骤完整性
    - [ ] 架构图逻辑正确性
    
    **商业文档检查**：
    - [ ] 数据来源可靠性
    - [ ] 财务计算准确性
    - [ ] 风险评估全面性
    - [ ] 执行计划可行性
    
    **学术文档检查**：
    - [ ] 引用格式规范性
    - [ ] 研究方法科学性
    - [ ] 数据分析严谨性
    - [ ] 结论逻辑一致性
  </process>

  <criteria>
    ## 多类型文档质量标准
    
    ### 通用质量指标
    - ✅ 类型识别准确率 = 100%
    - ✅ 格式规范符合度 ≥ 95%
    - ✅ 内容完整度 ≥ 90%
    - ✅ 专业术语准确度 = 100%
    
    ### 技术文档专项指标
    - ✅ 技术准确性 = 100%
    - ✅ 可操作性 ≥ 90%
    - ✅ 代码示例有效性 = 100%
    - ✅ 架构图清晰度 ≥ 85%
    
    ### 商业文档专项指标
    - ✅ 数据可靠性 = 100%
    - ✅ 商业逻辑合理性 ≥ 90%
    - ✅ 执行可行性 ≥ 85%
    - ✅ ROI计算准确性 = 100%
    
    ### 学术文档专项指标
    - ✅ 引用规范性 = 100%
    - ✅ 研究严谨性 ≥ 95%
    - ✅ 逻辑一致性 ≥ 90%
    - ✅ 创新性体现 ≥ 80%
  </criteria>
</execution>
