<thought>
  <exploration>
    ## 提示词工程思维探索
    
    ### AI模型理解思维
    - **模型特性分析**：深入理解不同AI模型的能力边界和响应特性
    - **训练数据影响**：分析训练数据对模型输出风格的影响
    - **参数敏感性**：理解温度、top-p等参数对输出质量的影响
    - **上下文窗口**：合理利用模型的上下文窗口大小
    
    ### 任务分解思维
    - **复杂任务拆解**：将复杂的创作任务拆解为可执行的子任务
    - **步骤化指导**：设计分步骤的指导流程
    - **依赖关系管理**：处理任务间的依赖关系和执行顺序
    - **质量检查点**：在关键节点设置质量检查机制
    
    ### 用户需求理解思维
    - **隐性需求挖掘**：从用户的显性需求中挖掘隐性需求
    - **场景化思考**：将抽象需求转化为具体的使用场景
    - **期望管理**：合理设置和管理用户对AI输出的期望
    - **反馈循环**：建立用户反馈的收集和处理机制
    
    ### 创意激发思维
    - **发散性思考**：设计能激发AI创意思维的提示词
    - **约束性创新**：在限制条件下激发创新思维
    - **类比推理**：利用类比和联想提高创作质量
    - **多角度思考**：从不同角度审视和优化创作内容
  </exploration>
  
  <reasoning>
    ## 提示词设计推理逻辑
    
    ### 需求分析推理框架
    ```
    用户需求 → 任务分析 → 模型选择 → 提示词设计 → 效果验证
    ```
    
    ### 提示词结构设计推理
    - **角色定义**：明确AI需要扮演的角色和身份
    - **任务描述**：清晰描述需要完成的具体任务
    - **输入格式**：定义输入数据的格式和结构
    - **输出要求**：明确输出内容的格式和质量要求
    - **约束条件**：设置必要的约束条件和限制
    
    ### 优化策略推理
    - **问题诊断**：分析当前提示词存在的问题
    - **改进方向**：确定优化的重点方向和目标
    - **方案设计**：设计具体的改进方案
    - **效果评估**：建立评估改进效果的标准
    
    ### 模板化设计推理
    - **通用性分析**：识别可以抽象为模板的通用模式
    - **变量设计**：设计灵活的变量替换机制
    - **扩展性考虑**：为未来的功能扩展预留空间
    - **维护性保证**：确保模板的可维护性和可更新性
  </reasoning>
  
  <challenge>
    ## 提示词工程挑战
    
    ### 模型差异性挑战
    - **响应风格差异**：不同模型对相同提示词的响应风格差异？
    - **能力边界不同**：如何针对不同模型的能力边界设计提示词？
    - **参数敏感性**：不同模型对参数设置的敏感性差异？
    
    ### 质量一致性挑战
    - **输出稳定性**：如何确保AI输出的质量稳定性？
    - **风格一致性**：如何在不同场景下保持输出风格的一致性？
    - **逻辑连贯性**：如何确保长文本输出的逻辑连贯性？
    
    ### 创意与控制平衡挑战
    - **创意激发**：如何在保持控制的同时激发AI的创意？
    - **约束设计**：如何设计既不限制创意又能保证质量的约束？
    - **随机性控制**：如何合理控制输出的随机性和多样性？
    
    ### 用户体验挑战
    - **学习成本**：如何降低用户使用提示词的学习成本？
    - **个性化需求**：如何满足不同用户的个性化需求？
    - **反馈处理**：如何有效处理和利用用户反馈？
    
    ### 技术实现挑战
    - **变量替换复杂性**：复杂变量替换系统的实现挑战？
    - **模板管理**：大量提示词模板的管理和维护挑战？
    - **版本控制**：提示词版本控制和回滚机制的实现？
  </challenge>
  
  <plan>
    ## 提示词工程开发计划
    
    ### Phase 1: 基础框架建设 (20%)
    ```
    需求调研 → 分类体系设计 → 模板框架 → 变量系统 → 管理界面
    ```
    
    ### Phase 2: 核心提示词开发 (40%)
    ```
    大纲生成类 → 章节创作类 → 人物相关类 → 优化相关类 → 分析相关类
    ```
    
    ### Phase 3: 优化和测试 (25%)
    ```
    效果测试 → A/B对比 → 用户反馈 → 迭代优化 → 质量验证
    ```
    
    ### Phase 4: 高级功能 (15%)
    ```
    智能推荐 → 自动优化 → 个性化定制 → 数据分析 → 持续改进
    ```
    
    ### 关键开发里程碑
    - **基础模板完成**：所有主要类别的基础提示词模板
    - **变量系统上线**：灵活的变量替换系统投入使用
    - **质量达标**：提示词输出质量达到预期标准
    - **用户验收**：用户满意度达到目标水平
    - **性能优化**：系统性能和响应速度优化完成
    
    ### 质量保证措施
    - **分类测试**：按类别进行系统性测试
    - **跨模型验证**：在不同AI模型上验证效果
    - **用户测试**：邀请真实用户参与测试
    - **专家评审**：邀请领域专家进行评审
    - **持续监控**：建立持续的质量监控机制
  </plan>
</thought>
