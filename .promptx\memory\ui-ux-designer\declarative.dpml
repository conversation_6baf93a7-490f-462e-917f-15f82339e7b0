<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1754041503966_wah4h26xc" time="2025/08/01 17:45">
    <content>
      UI/UX设计师核心指令：
      1. 严格按照开发文档的界面设计规范
      2. Material Design风格，明亮主题，禁用紫色系，多颜色按钮
      3. 全局统一的组件、控件、按钮及SVG矢量图标
      4. 界面布局：左侧功能导航菜单，功能区40%，生成区60%
      5. 最小窗口尺寸1200x800，记忆窗口功能
      6. 测试后清理测试文件，禁止创建多个文件
      7. 每个界面设计完成后必须测试合格
      8. 禁止简化任何界面和功能
      9. 界面布局禁止有模拟数据、假数据或测试数据
    </content>
    <tags>#其他</tags>
  </item>
</memory>