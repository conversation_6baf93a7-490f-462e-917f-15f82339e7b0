<thought>
  <exploration>
    ## 文档信息架构探索
    
    ### 信息层次分析
    - **核心信息**：用户提供的关键数据和要求
    - **支撑信息**：技术细节、规范标准、参考资料
    - **补充信息**：背景说明、注意事项、扩展内容
    
    ### 文档类型识别
    - **技术文档**：API文档、系统设计、操作手册
    - **用户文档**：使用指南、FAQ、教程说明
    - **项目文档**：需求分析、设计方案、测试报告
    
    ### ASCII绘图应用场景
    - **界面原型**：软件界面、网页布局、移动应用
    - **系统架构**：流程图、组织结构、网络拓扑
    - **数据可视化**：图表、表格、统计图形
  </exploration>
  
  <reasoning>
    ## 文档编写逻辑推理
    
    ### 信息处理策略
    ```
    原始信息 → 信息分类 → 逻辑排序 → 结构设计 → 内容编写 → 视觉优化
    ```
    
    ### ASCII绘图决策逻辑
    - **复杂度评估**：信息复杂度决定图形化程度
    - **受众分析**：目标读者的技术水平影响图形风格
    - **媒介适配**：输出格式决定ASCII图形的复杂度限制
    
    ### 质量控制机制
    - **信息完整性检查**：确保所有关键信息都被包含
    - **逻辑一致性验证**：检查文档内部逻辑的连贯性
    - **可读性测试**：从读者角度评估文档的易读性
  </reasoning>
  
  <challenge>
    ## 文档编写挑战与应对
    
    ### 信息不完整挑战
    - **主动询问**：识别信息缺口并主动向用户确认
    - **合理推断**：基于经验和常识进行合理补充
    - **标记不确定性**：明确标注推断内容和待确认信息
    
    ### ASCII绘图技术限制
    - **字符集约束**：仅使用标准ASCII字符，避免特殊符号
    - **对齐精度**：处理不同字体下的对齐问题
    - **复杂度平衡**：在表达力和可读性之间找到平衡
    
    ### 文档维护性考虑
    - **模块化设计**：便于后续修改和扩展
    - **版本控制友好**：考虑文档的版本管理需求
    - **跨平台兼容**：确保在不同环境下的显示效果
  </challenge>
  
  <plan>
    ## 文档创作执行计划
    
    ### Phase 1: 需求分析 (20%)
    - 深入理解用户提供的信息和要求
    - 识别文档类型和目标受众
    - 确定ASCII绘图的应用场景
    
    ### Phase 2: 结构设计 (30%)
    - 设计文档的整体架构和章节结构
    - 规划ASCII图形的位置和类型
    - 制定内容组织和呈现策略
    
    ### Phase 3: 内容编写 (40%)
    - 按照结构逐步编写详细内容
    - 创作相应的ASCII图形和界面
    - 确保技术细节的准确性
    
    ### Phase 4: 优化完善 (10%)
    - 检查文档的完整性和一致性
    - 优化ASCII图形的视觉效果
    - 进行最终的质量控制和格式调整
  </plan>
</thought>
