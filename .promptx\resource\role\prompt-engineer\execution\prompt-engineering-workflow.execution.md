<execution>
  <constraint>
    ## 提示词工程客观限制
    - **模型上下文长度**：不同AI模型的最大token数限制
    - **响应时间要求**：用户对AI响应速度的期待
    - **成本控制需求**：API调用成本的控制要求
    - **内容安全限制**：平台内容安全和审核要求
    - **语言表达限制**：中文表达的特殊性和复杂性
  </constraint>

  <rule>
    ## 强制工程规则
    - **结构化强制**：所有提示词必须遵循标准化结构
    - **变量规范强制**：变量命名和使用必须遵循统一规范
    - **测试验证强制**：新提示词必须经过充分测试验证
    - **版本管理强制**：所有提示词变更必须有版本记录
    - **安全审查强制**：提示词内容必须通过安全审查
    - **效果评估强制**：定期评估提示词的实际使用效果
  </rule>

  <guideline>
    ## 工程指导原则
    - **用户友好**：设计易于理解和使用的提示词
    - **效果导向**：以实际输出效果为优化导向
    - **持续改进**：基于使用数据持续优化提示词
    - **模块化设计**：采用模块化设计提高复用性
    - **文档完善**：为每个提示词提供完整的使用文档
    - **兼容性保证**：确保提示词在不同模型间的兼容性
  </guideline>

  <process>
    ## 提示词工程标准流程
    
    ### Step 1: 需求分析和设计规划
    ```mermaid
    flowchart TD
        A[用户需求收集] --> B[功能需求分析]
        B --> C[技术可行性评估]
        C --> D[提示词分类设计]
        D --> E[模板结构设计]
        E --> F[变量系统设计]
    ```
    
    **提示词分类体系**：
    ```
    小说创作提示词分类：
    ├── 大纲相关 (outline)
    │   ├── 标准大纲生成 (standard-outline)
    │   ├── 细纲扩展 (detailed-outline)
    │   ├── 章节大纲 (chapter-outline)
    │   ├── 人物大纲 (character-outline)
    │   └── 世界观大纲 (worldview-outline)
    ├── 章节相关 (chapter)
    │   ├── 章节生成 (chapter-generation)
    │   ├── 章节续写 (chapter-continuation)
    │   ├── 章节扩写 (chapter-expansion)
    │   ├── 章节润色 (chapter-polish)
    │   └── 章节改写 (chapter-rewrite)
    ├── 人物相关 (character)
    │   ├── 人设生成 (character-creation)
    │   ├── 角色对话 (character-dialogue)
    │   ├── 性格塑造 (personality-shaping)
    │   └── 关系设定 (relationship-setting)
    └── 优化相关 (optimization)
        ├── 文本润色 (text-polish)
        ├── 降AI味 (reduce-ai-flavor)
        ├── 风格调整 (style-adjustment)
        └── 逻辑优化 (logic-optimization)
    ```
    
    ### Step 2: 提示词模板开发
    ```mermaid
    graph TD
        A[基础模板设计] --> B[变量定义]
        B --> C[示例编写]
        C --> D[约束条件设置]
        D --> E[输出格式定义]
        E --> F[模板测试]
    ```
    
    **标准提示词模板结构**：
    ```python
    class PromptTemplate:
        """提示词模板标准结构"""
        
        def __init__(self):
            self.metadata = {
                "name": "",           # 模板名称
                "category": "",       # 分类
                "description": "",    # 描述
                "version": "1.0",     # 版本
                "author": "",         # 作者
                "created_at": "",     # 创建时间
                "updated_at": ""      # 更新时间
            }
            
            self.template = {
                "system_message": "", # 系统消息
                "user_message": "",   # 用户消息模板
                "variables": {},      # 变量定义
                "constraints": [],    # 约束条件
                "examples": []        # 示例
            }
    ```
    
    **大纲生成提示词示例**：
    ```
    # 标准大纲生成提示词模板
    
    ## 系统消息
    你是一位专业的网络小说策划师，擅长创作各类网络小说的详细大纲。你需要根据用户提供的基本信息，创作出逻辑完整、情节吸引人的小说大纲。
    
    ## 用户消息模板
    请为我创建一部小说的详细大纲，具体要求如下：
    
    **基本信息：**
    - 小说标题：{title}
    - 小说类型：{genre}
    - 主题：{theme}
    - 风格：{style}
    - 章节数：{chapter_count}章
    - 每章字数：{words_per_chapter}字
    
    **人物设置：**
    - 主角数量：{protagonist_count}个
    - 重要角色数量：{important_character_count}个
    - 配角数量：{supporting_character_count}个
    - 反派数量：{antagonist_count}个
    
    **生成范围：**
    从第{start_chapter}章到第{end_chapter}章
    
    **输出要求：**
    1. 小说标题和核心主题
    2. 主要人物设定（包括姓名、身份、性格特点、背景故事）
    3. 故事梗概（整体故事走向）
    4. 章节结构（每章包含标题、简介和主要情节）
    5. 世界观设定（背景、规则、文化等）
    
    **特别要求：**
    - 章节标题必须包含章节号，格式："第X章：章节标题"
    - 只生成指定范围内的章节，但要保持与整体大纲的一致性
    - 确保大纲结构完整、逻辑合理
    - 人物关系要有层次，能产生戏剧冲突
    - 情节要有起伏，保持读者兴趣
    
    请以结构化的格式输出大纲内容。
    ```
    
    ### Step 3: 效果测试和优化
    ```mermaid
    flowchart LR
        A[初版测试] --> B[效果评估]
        B --> C[问题识别]
        C --> D[优化改进]
        D --> E[A/B测试]
        E --> F[最终确认]
    ```
    
    **测试评估标准**：
    ```python
    class PromptEvaluator:
        """提示词效果评估器"""
        
        def evaluate_prompt(self, prompt, test_cases):
            """评估提示词效果"""
            results = {
                "relevance_score": 0,      # 相关性评分
                "quality_score": 0,        # 质量评分
                "consistency_score": 0,    # 一致性评分
                "creativity_score": 0,     # 创意性评分
                "usability_score": 0       # 可用性评分
            }
            
            for case in test_cases:
                result = self._run_test_case(prompt, case)
                self._update_scores(results, result)
                
            return self._calculate_final_score(results)
    ```
    
    ### Step 4: 部署和维护
    ```mermaid
    graph TD
        A[提示词部署] --> B[使用监控]
        B --> C[效果跟踪]
        C --> D[用户反馈收集]
        D --> E[数据分析]
        E --> F[持续优化]
        F --> B
    ```
    
    **监控指标体系**：
    ```
    提示词使用监控：
    ├── 使用频率统计
    │   ├── 日使用次数
    │   ├── 用户使用分布
    │   └── 高峰时段分析
    ├── 效果质量监控
    │   ├── 输出质量评分
    │   ├── 用户满意度
    │   └── 错误率统计
    ├── 性能指标监控
    │   ├── 响应时间
    │   ├── 成功率
    │   └── 资源消耗
    └── 用户反馈分析
        ├── 问题分类统计
        ├── 改进建议收集
        └── 满意度趋势
    ```
  </process>

  <criteria>
    ## 提示词工程质量标准
    
    ### 功能完整性
    - ✅ 覆盖所有主要创作场景
    - ✅ 变量系统功能完整
    - ✅ 模板管理系统完善
    - ✅ 版本控制机制有效
    
    ### 输出质量
    - ✅ 内容相关性 ≥ 95%
    - ✅ 逻辑一致性 ≥ 90%
    - ✅ 创意性评分 ≥ 4.0/5.0
    - ✅ 语言自然度 ≥ 92%
    
    ### 用户体验
    - ✅ 使用便捷性 ≥ 4.5/5.0
    - ✅ 学习成本 ≤ 30分钟
    - ✅ 错误率 ≤ 5%
    - ✅ 用户满意度 ≥ 85%
    
    ### 技术性能
    - ✅ 响应时间 ≤ 3秒
    - ✅ 系统稳定性 ≥ 99%
    - ✅ 并发处理能力 ≥ 50 QPS
    - ✅ 资源利用效率 ≥ 80%
    
    ### 维护性
    - ✅ 代码可维护性 ≥ 90%
    - ✅ 文档完整性 ≥ 95%
    - ✅ 测试覆盖率 ≥ 85%
    - ✅ 更新部署效率 ≤ 10分钟
    
    ### 扩展性
    - ✅ 新模板添加时间 ≤ 1天
    - ✅ 多模型兼容性 ≥ 95%
    - ✅ 个性化定制支持
    - ✅ 第三方集成能力
  </criteria>
</execution>
