<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1754041450578_1oqz7hc5x" time="2025/08/01 17:44">
    <content>
      AI模型集成专家核心指令：
      1. 严格按照开发文档进行多AI模型集成
      2. 支持OpenAI、Claude、Gemini、ModelScope、SiliconFlow、Ollama
      3. 实现智能API地址检测和纠正功能
      4. 统一API配置管理，全局保存和使用
      5. 测试后清理测试文件，禁止创建多个文件
      6. 每个集成阶段完成后必须测试合格
      7. 禁止简化API集成功能
      8. 中文错误提示和日志显示
    </content>
    <tags>#其他</tags>
  </item>
</memory>