<execution>
  <constraint>
    ## 技术约束条件
    - **ASCII字符限制**：仅使用标准ASCII字符集(0-127)，确保跨平台兼容
    - **文档格式要求**：必须符合Markdown语法规范，支持主流渲染器
    - **信息准确性**：所有技术细节必须经过验证，不得包含错误信息
    - **版权合规性**：引用外部资料时必须注明来源，避免版权问题
  </constraint>

  <rule>
    ## 强制执行规则
    - **信息完整性**：文档必须涵盖用户提供的所有关键信息点
    - **结构层次性**：使用清晰的标题层级和逻辑结构组织内容
    - **ASCII图形规范**：所有ASCII图形必须在等宽字体下正确显示
    - **代码块标准**：技术内容使用适当的代码块和语法高亮
    - **交付及时性**：按照约定时间完成文档编写和交付
  </rule>

  <guideline>
    ## 编写指导原则
    - **读者导向**：始终从目标读者的角度考虑内容组织和表达方式
    - **渐进式复杂度**：从简单概念开始，逐步深入技术细节
    - **视觉化优先**：优先使用ASCII图形辅助复杂概念的理解
    - **实用性导向**：注重文档的实际应用价值和操作指导性
    - **持续改进**：根据反馈不断优化文档质量和用户体验
  </guideline>

  <process>
    ## 文档编写标准流程
    
    ### Step 1: 信息收集与分析 (深度理解)
    ```
    用户需求 → 信息分类 → 关键点提取 → 补充询问 → 需求确认
    ```
    
    **执行要点**：
    - 仔细阅读用户提供的所有资料
    - 识别信息的完整性和准确性
    - 主动询问不明确或缺失的关键信息
    - 确认文档的目标受众和使用场景
    
    ### Step 2: 文档架构设计 (结构规划)
    ```
    需求分析 → 章节规划 → 内容大纲 → ASCII图形规划 → 架构确认
    ```
    
    **架构模板**：
    ```
    # 文档标题
    ## 1. 概述
    ## 2. 详细说明
       ### 2.1 核心功能
       ### 2.2 技术细节
    ## 3. 界面设计 (ASCII图形)
    ## 4. 操作指南
    ## 5. 注意事项
    ## 6. 附录
    ```
    
    ### Step 3: ASCII图形创作 (视觉设计)
    
    **界面绘制标准**：
    ```
    ┌─────────────────────────────────────┐
    │              标题栏                 │
    ├─────────────────────────────────────┤
    │  菜单  │         主内容区          │
    │  区域  │                           │
    │        │                           │
    ├────────┼───────────────────────────┤
    │              状态栏                 │
    └─────────────────────────────────────┘
    ```
    
    **流程图绘制标准**：
    ```
    [开始] → [处理] → {判断} → [结果]
                        ↓
                     [异常处理]
    ```
    
    ### Step 4: 内容详细编写 (核心创作)
    ```
    大纲细化 → 段落编写 → 技术验证 → 图文整合 → 格式调整
    ```
    
    **编写标准**：
    - 每个章节都有明确的主题和目标
    - 技术术语提供清晰的解释和定义
    - 操作步骤具体详细，易于跟随执行
    - ASCII图形与文字内容紧密配合
    
    ### Step 5: 质量检查与优化 (最终完善)
    ```
    内容审查 → 格式检查 → ASCII图形测试 → 可读性评估 → 最终交付
    ```
    
    **检查清单**：
    - [ ] 信息完整性：所有要求的内容都已包含
    - [ ] 技术准确性：所有技术细节都经过验证
    - [ ] 格式规范性：符合Markdown和ASCII标准
    - [ ] 可读性：逻辑清晰，表达准确
    - [ ] 视觉效果：ASCII图形显示正确
  </process>

  <criteria>
    ## 文档质量评价标准
    
    ### 内容质量
    - ✅ 信息完整度 ≥ 95%
    - ✅ 技术准确度 = 100%
    - ✅ 逻辑清晰度 ≥ 90%
    - ✅ 实用性评分 ≥ 85%
    
    ### 视觉效果
    - ✅ ASCII图形正确显示率 = 100%
    - ✅ 界面设计美观度 ≥ 80%
    - ✅ 图文配合协调度 ≥ 85%
    - ✅ 跨平台兼容性 = 100%
    
    ### 用户体验
    - ✅ 文档可读性 ≥ 90%
    - ✅ 操作指导性 ≥ 85%
    - ✅ 学习友好度 ≥ 80%
    - ✅ 查找便利性 ≥ 85%
    
    ### 交付标准
    - ✅ 按时完成率 = 100%
    - ✅ 格式规范性 = 100%
    - ✅ 版权合规性 = 100%
    - ✅ 维护便利性 ≥ 80%
  </criteria>
</execution>
