<execution>
  <constraint>
    ## PySide6开发客观限制
    - **Qt6版本依赖**：必须使用Qt6.2+版本，确保API稳定性
    - **Python版本兼容**：支持Python 3.8-3.12版本范围
    - **内存使用限制**：桌面应用内存使用需控制在合理范围内
    - **启动时间约束**：应用启动时间需控制在3秒以内
    - **跨平台兼容性**：需要在Windows、macOS、Linux上正常运行
  </constraint>

  <rule>
    ## 强制开发规则
    - **MVC架构强制**：必须严格遵循Model-View-Controller架构
    - **信号槽机制**：UI事件处理必须使用Qt信号槽机制
    - **线程安全规则**：UI操作必须在主线程中执行
    - **资源管理规则**：所有Qt对象必须正确管理生命周期
    - **样式表规范**：必须使用QSS样式表实现Material Design
    - **错误处理强制**：所有可能的异常都必须有适当的处理
  </rule>

  <guideline>
    ## 开发指导原则
    - **用户体验优先**：所有设计决策以用户体验为第一考虑
    - **性能与美观平衡**：在保证性能的前提下追求视觉效果
    - **代码可维护性**：编写清晰、可维护的代码
    - **组件化开发**：优先使用和开发可复用组件
    - **渐进式增强**：从基础功能开始，逐步增加高级特性
    - **测试驱动开发**：重要功能需要编写单元测试
  </guideline>

  <process>
    ## PySide6开发标准流程
    
    ### Step 1: 项目初始化和架构设计
    ```mermaid
    flowchart TD
        A[需求分析] --> B[技术选型确认]
        B --> C[项目结构设计]
        C --> D[依赖管理配置]
        D --> E[开发环境搭建]
        E --> F[基础框架搭建]
    ```
    
    **项目结构标准**：
    ```
    ai_novel_assistant/
    ├── main.py                    # 应用程序入口
    ├── config/                    # 配置管理
    ├── core/                      # 核心业务逻辑
    ├── ui/                        # 用户界面
    │   ├── components/            # UI组件
    │   ├── dialogs/               # 对话框
    │   └── styles/                # 样式文件
    ├── utils/                     # 工具函数
    ├── resources/                 # 资源文件
    └── tests/                     # 测试文件
    ```
    
    ### Step 2: UI组件开发
    ```mermaid
    graph TD
        A[设计UI原型] --> B[创建基础组件]
        B --> C[实现Material Design样式]
        C --> D[添加交互逻辑]
        D --> E[组件测试验证]
        E --> F[组件文档编写]
    ```
    
    **Material Design实现标准**：
    ```python
    # 颜色系统定义
    MATERIAL_COLORS = {
        'primary': '#1976D2',      # 蓝色主色
        'secondary': '#FFC107',    # 琥珀色辅助色
        'surface': '#FFFFFF',      # 表面色
        'background': '#FAFAFA',   # 背景色
        'error': '#F44336',        # 错误色
        'success': '#4CAF50',      # 成功色
        'warning': '#FF9800',      # 警告色
    }
    
    # 组件样式模板
    BUTTON_STYLE = """
    QPushButton {
        background-color: {primary};
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        font-weight: 500;
    }
    QPushButton:hover {
        background-color: {primary_dark};
    }
    """
    ```
    
    ### Step 3: 业务逻辑集成
    ```mermaid
    flowchart LR
        A[UI组件] --> B[信号发射]
        B --> C[控制器处理]
        C --> D[业务逻辑调用]
        D --> E[数据模型更新]
        E --> F[UI状态更新]
    ```
    
    **信号槽连接标准**：
    ```python
    # 新式信号槽连接语法
    self.button.clicked.connect(self.on_button_clicked)
    
    # 自定义信号定义
    class CustomWidget(QWidget):
        dataChanged = Signal(dict)  # 自定义信号
        
        def emit_data_change(self, data):
            self.dataChanged.emit(data)
    ```
    
    ### Step 4: 性能优化和测试
    ```mermaid
    graph TD
        A[性能分析] --> B[瓶颈识别]
        B --> C[优化实现]
        C --> D[功能测试]
        D --> E[性能测试]
        E --> F[用户体验测试]
    ```
    
    **性能优化检查清单**：
    - [ ] UI渲染性能优化
    - [ ] 内存使用监控
    - [ ] 异步操作实现
    - [ ] 资源加载优化
    - [ ] 启动速度优化
    
    ### Step 5: 打包和部署
    ```mermaid
    flowchart TD
        A[代码完成] --> B[依赖检查]
        B --> C[PyInstaller配置]
        C --> D[打包测试]
        D --> E[安装程序制作]
        E --> F[多平台测试]
    ```
    
    **PyInstaller配置示例**：
    ```python
    # build.spec
    a = Analysis(
        ['main.py'],
        pathex=[],
        binaries=[],
        datas=[('resources', 'resources')],
        hiddenimports=['PySide6.QtCore', 'PySide6.QtWidgets'],
        hookspath=[],
        hooksconfig={},
        runtime_hooks=[],
        excludes=[],
        win_no_prefer_redirects=False,
        win_private_assemblies=False,
        cipher=None,
        noarchive=False,
    )
    ```
  </process>

  <criteria>
    ## PySide6开发质量标准
    
    ### 功能完整性
    - ✅ 所有UI组件功能正常
    - ✅ 信号槽机制工作正确
    - ✅ 数据绑定和状态管理有效
    - ✅ 错误处理覆盖完整
    
    ### 性能指标
    - ✅ 应用启动时间 ≤ 3秒
    - ✅ UI响应时间 ≤ 100ms
    - ✅ 内存使用 ≤ 200MB（空闲状态）
    - ✅ CPU使用率 ≤ 5%（空闲状态）
    
    ### 用户体验
    - ✅ Material Design风格一致性 ≥ 95%
    - ✅ 界面布局响应式适配
    - ✅ 窗口状态记忆功能正常
    - ✅ 多语言支持（中文优先）
    
    ### 代码质量
    - ✅ 代码覆盖率 ≥ 80%
    - ✅ 代码规范符合PEP 8
    - ✅ 文档完整性 ≥ 90%
    - ✅ 无内存泄漏和资源泄漏
    
    ### 兼容性
    - ✅ Windows 10/11兼容性
    - ✅ macOS 10.15+兼容性
    - ✅ Ubuntu 20.04+兼容性
    - ✅ Python 3.8-3.12兼容性
    
    ### 部署质量
    - ✅ 打包程序正常运行
    - ✅ 安装程序制作完成
    - ✅ 依赖完全内置
    - ✅ 卸载程序功能正常
  </criteria>
</execution>
