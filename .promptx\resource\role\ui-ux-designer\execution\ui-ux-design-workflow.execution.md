<execution>
  <constraint>
    ## UI/UX设计客观限制
    - **屏幕尺寸约束**：最小1200x800像素的窗口尺寸限制
    - **技术实现约束**：PySide6框架的UI组件和样式限制
    - **性能约束**：界面渲染性能和内存使用限制
    - **平台兼容性**：Windows、macOS、Linux跨平台兼容要求
    - **可访问性要求**：符合无障碍设计标准的要求
  </constraint>

  <rule>
    ## 强制设计规则
    - **Material Design遵循**：必须严格遵循Material Design设计规范
    - **颜色系统限制**：禁用紫色系，使用明亮主题和多彩按钮
    - **布局比例固定**：功能区40%，生成区60%的布局比例
    - **图标规范强制**：只能使用SVG矢量图标，禁用emoji表情包
    - **一致性强制**：全局UI风格和交互模式必须保持一致
    - **响应式设计**：界面必须支持窗口大小调整和适配
  </rule>

  <guideline>
    ## 设计指导原则
    - **用户体验优先**：所有设计决策以用户体验为第一考虑
    - **简洁性原则**：保持界面简洁，避免不必要的复杂性
    - **直观性原则**：界面操作应该直观易懂，符合用户预期
    - **效率性原则**：优化常用功能的操作路径和效率
    - **美观性原则**：在保证功能的前提下追求视觉美感
    - **可扩展性**：为未来功能扩展预留设计空间
  </guideline>

  <process>
    ## UI/UX设计标准流程
    
    ### Step 1: 用户研究和需求分析
    ```mermaid
    flowchart TD
        A[用户调研] --> B[用户画像]
        B --> C[需求分析]
        C --> D[使用场景]
        D --> E[功能优先级]
        E --> F[设计目标]
    ```
    
    **用户研究方法**：
    ```
    定性研究：
    ├── 用户访谈
    │   ├── 深度访谈
    │   ├── 焦点小组
    │   └── 用户日记
    ├── 观察研究
    │   ├── 用户观察
    │   ├── 任务分析
    │   └── 情境调研
    └── 专家评估
        ├── 启发式评估
        ├── 认知走查
        └── 专家访谈
    
    定量研究：
    ├── 问卷调查
    ├── 数据分析
    ├── A/B测试
    └── 可用性测试
    ```
    
    ### Step 2: 信息架构和交互设计
    ```mermaid
    graph TD
        A[功能梳理] --> B[信息分类]
        B --> C[导航设计]
        C --> D[用户流程]
        D --> E[交互原型]
        E --> F[可用性验证]
    ```
    
    **信息架构设计标准**：
    ```
    AI小说助手信息架构：
    ├── 主导航（左侧菜单）
    │   ├── 首页仪表盘
    │   ├── 创作功能组
    │   │   ├── 大纲生成
    │   │   ├── 大纲编辑
    │   │   ├── 章节编辑
    │   │   ├── 章节生成
    │   │   └── 章节分析
    │   ├── 管理功能组
    │   │   ├── 人物编辑
    │   │   ├── 人物关系图
    │   │   └── 统计信息
    │   └── 工具功能组
    │       ├── AI聊天
    │       ├── 提示词库
    │       ├── 上下文管理
    │       ├── 向量库检索
    │       └── 设置
    ├── 顶部工具栏
    │   ├── 文件操作
    │   ├── 编辑操作
    │   ├── 视图控制
    │   └── 帮助信息
    └── 底部状态栏
        ├── 状态信息
        ├── 进度显示
        └── 统计数据
    ```
    
    ### Step 3: 视觉设计系统
    ```mermaid
    mindmap
      root((视觉设计系统))
        色彩系统
          主色调
          辅助色
          功能色
          中性色
        字体系统
          标题字体
          正文字体
          代码字体
          图标字体
        组件系统
          基础组件
          复合组件
          业务组件
          布局组件
        图标系统
          功能图标
          状态图标
          装饰图标
          品牌图标
    ```
    
    **Material Design色彩系统**：
    ```css
    /* AI小说助手色彩规范 */
    :root {
      /* 主色调 - 蓝色系 */
      --primary-50: #E3F2FD;
      --primary-100: #BBDEFB;
      --primary-500: #2196F3;
      --primary-700: #1976D2;
      --primary-900: #0D47A1;
      
      /* 辅助色 - 绿色系 */
      --secondary-50: #E8F5E8;
      --secondary-500: #4CAF50;
      --secondary-700: #388E3C;
      
      /* 功能色 */
      --error: #F44336;
      --warning: #FF9800;
      --info: #2196F3;
      --success: #4CAF50;
      
      /* 中性色 */
      --surface: #FFFFFF;
      --background: #FAFAFA;
      --on-surface: #212121;
      --on-background: #424242;
    }
    ```
    
    ### Step 4: 界面布局设计
    ```mermaid
    graph LR
        A[栅格系统] --> B[布局框架]
        B --> C[组件布局]
        C --> D[响应式适配]
        D --> E[细节优化]
    ```
    
    **标准界面布局模板**：
    ```
    ┌─────────────────────────────────────────────────────────────────────────────┐
    │  [菜单] [工具栏]                                    [窗口控制]              │
    ├─────────────────────────────────────────────────────────────────────────────┤
    │                                                                             │
    │  ┌─────────────────────────┐  ┌─────────────────────────────────────────┐  │
    │  │                         │  │                                         │  │
    │  │      功能控制区域        │  │            内容展示区域                 │  │
    │  │        (40%)           │  │             (60%)                      │  │
    │  │                         │  │                                         │  │
    │  │  ┌─────────────────────┐ │  │  ┌─────────────────────────────────────┐ │  │
    │  │  │    参数设置区       │ │  │  │         主要内容区                  │ │  │
    │  │  └─────────────────────┘ │  │  └─────────────────────────────────────┘ │  │
    │  │                         │  │                                         │  │
    │  │  ┌─────────────────────┐ │  │  ┌─────────────────────────────────────┐ │  │
    │  │  │    操作按钮区       │ │  │  │         辅助信息区                  │ │  │
    │  │  └─────────────────────┘ │  │  └─────────────────────────────────────┘ │  │
    │  │                         │  │                                         │  │
    │  └─────────────────────────┘  └─────────────────────────────────────────┘  │
    │                                                                             │
    ├─────────────────────────────────────────────────────────────────────────────┤
    │ [状态信息] [进度显示] [统计数据]                                            │
    └─────────────────────────────────────────────────────────────────────────────┘
    ```
    
    ### Step 5: 交互动效设计
    ```mermaid
    flowchart LR
        A[动效规划] --> B[缓动函数]
        B --> C[时长设置]
        C --> D[状态转换]
        D --> E[用户反馈]
    ```
    
    **Material Motion动效规范**：
    ```python
    # 动效时长标准
    ANIMATION_DURATION = {
        'micro': 100,      # 微交互 (100ms)
        'short': 200,      # 短动画 (200ms)
        'medium': 300,     # 中等动画 (300ms)
        'long': 500,       # 长动画 (500ms)
        'extra_long': 700  # 超长动画 (700ms)
    }
    
    # 缓动函数
    EASING_FUNCTIONS = {
        'standard': 'cubic-bezier(0.4, 0.0, 0.2, 1)',
        'decelerate': 'cubic-bezier(0.0, 0.0, 0.2, 1)',
        'accelerate': 'cubic-bezier(0.4, 0.0, 1, 1)',
        'sharp': 'cubic-bezier(0.4, 0.0, 0.6, 1)'
    }
    ```
  </process>

  <criteria>
    ## UI/UX设计质量标准
    
    ### 视觉设计质量
    - ✅ Material Design规范符合率 ≥ 95%
    - ✅ 色彩对比度符合WCAG标准
    - ✅ 字体层次清晰度 ≥ 90%
    - ✅ 图标一致性 ≥ 98%
    
    ### 用户体验质量
    - ✅ 任务完成率 ≥ 90%
    - ✅ 任务完成时间减少 ≥ 30%
    - ✅ 用户满意度 ≥ 4.5/5.0
    - ✅ 学习成本 ≤ 30分钟
    
    ### 交互设计质量
    - ✅ 操作响应时间 ≤ 100ms
    - ✅ 错误率 ≤ 5%
    - ✅ 操作路径优化率 ≥ 25%
    - ✅ 界面一致性 ≥ 95%
    
    ### 技术实现质量
    - ✅ 界面渲染性能 ≥ 60fps
    - ✅ 内存使用优化 ≤ 100MB
    - ✅ 跨平台兼容性 ≥ 98%
    - ✅ 响应式适配完整性 100%
    
    ### 可维护性
    - ✅ 设计系统完整性 ≥ 90%
    - ✅ 组件复用率 ≥ 80%
    - ✅ 设计文档完整性 ≥ 95%
    - ✅ 开发协作效率 ≥ 85%
    
    ### 可访问性
    - ✅ 键盘导航支持 100%
    - ✅ 屏幕阅读器兼容性 ≥ 90%
    - ✅ 色盲友好设计 100%
    - ✅ 多语言支持准备 ≥ 80%
  </criteria>
</execution>
