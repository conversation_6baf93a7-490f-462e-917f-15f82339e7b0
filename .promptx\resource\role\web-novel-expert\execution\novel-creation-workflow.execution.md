<execution>
  <constraint>
    ## 网络小说创作客观限制
    - **平台规则约束**：各平台的内容审核和发布规则限制
    - **更新频率要求**：读者期待的稳定更新频率压力
    - **字数要求**：不同平台和类型的章节字数要求
    - **题材限制**：某些敏感题材的创作限制
    - **版权保护**：原创性要求和抄袭风险防范
  </constraint>

  <rule>
    ## 强制创作规则
    - **原创性强制**：所有内容必须保证原创性，避免抄袭
    - **逻辑一致性**：故事情节和人物行为必须符合内在逻辑
    - **人物设定一致**：角色性格和能力设定必须前后一致
    - **世界观统一**：世界观设定必须内部统一，不能自相矛盾
    - **语言规范**：使用规范的中文表达，避免语法错误
    - **内容健康**：内容必须符合平台规范和社会价值观
  </rule>

  <guideline>
    ## 创作指导原则
    - **读者导向**：始终以读者需求和体验为创作导向
    - **质量优先**：在保证更新的前提下优先保证内容质量
    - **创新求变**：在遵循类型规律的基础上追求创新
    - **情感真实**：角色情感和关系要真实可信
    - **节奏把控**：合理控制故事节奏，保持读者兴趣
    - **细节丰富**：通过细节描写增强故事的真实感
  </guideline>

  <process>
    ## 网络小说创作标准流程
    
    ### Step 1: 前期策划和大纲设计
    ```mermaid
    flowchart TD
        A[市场调研] --> B[题材确定]
        B --> C[核心设定]
        C --> D[主要人物设计]
        D --> E[故事大纲]
        E --> F[章节规划]
    ```
    
    **大纲设计标准**：
    ```
    1. 故事核心
       - 核心冲突：主角面临的主要挑战
       - 主题思想：故事要表达的核心理念
       - 目标读者：明确的读者群体定位
    
    2. 人物设定
       - 主角：姓名、年龄、性格、背景、目标、能力
       - 重要配角：关系、作用、性格特点
       - 反派角色：动机、能力、与主角的冲突
    
    3. 世界观设定
       - 背景设定：时代、地点、社会结构
       - 规则体系：力量体系、等级划分
       - 文化背景：风俗、制度、价值观
    
    4. 情节结构
       - 开端：世界观介绍、人物登场
       - 发展：冲突升级、人物成长
       - 高潮：核心冲突爆发
       - 结局：冲突解决、人物归宿
    ```
    
    ### Step 2: 章节创作流程
    ```mermaid
    graph TD
        A[章节规划] --> B[场景设计]
        B --> C[对话编写]
        C --> D[动作描写]
        D --> E[心理描写]
        E --> F[环境描写]
        F --> G[章节检查]
        G --> H[发布准备]
    ```
    
    **章节创作标准**：
    ```python
    class ChapterStructure:
        """章节结构标准"""
        
        def __init__(self):
            self.hook = ""          # 开头吸引点
            self.development = ""   # 情节发展
            self.conflict = ""      # 冲突展现
            self.resolution = ""    # 部分解决
            self.cliffhanger = ""   # 结尾悬念
            
        def validate_chapter(self):
            """章节质量检查"""
            checks = [
                self._check_word_count(),      # 字数检查
                self._check_dialogue_ratio(),  # 对话比例
                self._check_pacing(),          # 节奏检查
                self._check_character_consistency(), # 人物一致性
                self._check_plot_advancement() # 情节推进
            ]
            return all(checks)
    ```
    
    ### Step 3: 人物塑造流程
    ```mermaid
    mindmap
      root((人物塑造))
        基础设定
          外貌特征
          性格特点
          背景经历
          能力特长
        关系网络
          家庭关系
          朋友关系
          敌对关系
          爱情关系
        成长轨迹
          初始状态
          成长动机
          关键转折
          最终状态
        对话风格
          语言特色
          说话习惯
          情感表达
          思维方式
    ```
    
    **人物设定模板**：
    ```
    角色档案：
    ├── 基本信息
    │   ├── 姓名：[角色姓名]
    │   ├── 年龄：[具体年龄]
    │   ├── 性别：[性别]
    │   └── 职业：[职业/身份]
    ├── 外貌特征
    │   ├── 身高体型：[具体描述]
    │   ├── 面部特征：[五官特点]
    │   └── 特殊标记：[疤痕、胎记等]
    ├── 性格特点
    │   ├── 核心性格：[3-5个关键词]
    │   ├── 优点：[主要优点]
    │   ├── 缺点：[主要缺点]
    │   └── 恐惧：[内心恐惧]
    ├── 背景经历
    │   ├── 出生背景：[家庭、地区]
    │   ├── 成长经历：[重要事件]
    │   ├── 教育经历：[学习背景]
    │   └── 工作经历：[职业发展]
    └── 关系网络
        ├── 家人：[家庭成员关系]
        ├── 朋友：[重要友谊]
        ├── 敌人：[对立关系]
        └── 爱情：[感情状况]
    ```
    
    ### Step 4: 质量控制和优化
    ```mermaid
    flowchart LR
        A[初稿完成] --> B[自我检查]
        B --> C[逻辑验证]
        C --> D[语言润色]
        D --> E[读者测试]
        E --> F[反馈收集]
        F --> G[修改优化]
        G --> H[最终发布]
    ```
    
    **质量检查清单**：
    ```
    内容质量检查：
    □ 情节逻辑是否合理
    □ 人物行为是否符合设定
    □ 对话是否自然流畅
    □ 描写是否生动具体
    □ 节奏是否张弛有度
    
    技术质量检查：
    □ 错别字和语法错误
    □ 标点符号使用规范
    □ 段落结构是否清晰
    □ 章节长度是否合适
    □ 格式是否符合平台要求
    
    读者体验检查：
    □ 开头是否吸引人
    □ 结尾是否有悬念
    □ 冲突是否足够激烈
    □ 情感是否能引起共鸣
    □ 整体阅读体验是否流畅
    ```
  </process>

  <criteria>
    ## 网络小说创作质量标准
    
    ### 内容质量
    - ✅ 故事逻辑完整性 ≥ 95%
    - ✅ 人物设定一致性 ≥ 98%
    - ✅ 情节推进合理性 ≥ 90%
    - ✅ 语言表达流畅度 ≥ 92%
    
    ### 读者反响
    - ✅ 读者留存率 ≥ 70%
    - ✅ 章节完读率 ≥ 85%
    - ✅ 读者评分 ≥ 4.0/5.0
    - ✅ 评论互动率 ≥ 15%
    
    ### 更新质量
    - ✅ 更新稳定性 ≥ 95%
    - ✅ 章节字数达标率 ≥ 98%
    - ✅ 内容原创性 100%
    - ✅ 平台规范符合率 100%
    
    ### 商业表现
    - ✅ 订阅转化率 ≥ 10%
    - ✅ 推荐位获得率 ≥ 30%
    - ✅ 榜单排名稳定性
    - ✅ 版权价值评估
    
    ### 创作效率
    - ✅ 日均创作字数 ≥ 3000字
    - ✅ 创作质量稳定性 ≥ 90%
    - ✅ 截稿时间准确率 ≥ 95%
    - ✅ 修改效率 ≤ 20%返工率
    
    ### 长期发展
    - ✅ 作品完结率 ≥ 80%
    - ✅ 读者粉丝积累增长率
    - ✅ 个人品牌建设效果
    - ✅ 多平台适应能力
  </criteria>
</execution>
