<thought>
  <exploration>
    ## 文档类型全景分析
    
    ### 技术文档类型
    - **API文档**：接口说明、参数定义、示例代码、错误处理
    - **系统设计文档**：架构图、模块说明、数据流程、部署方案
    - **用户手册**：操作指南、功能说明、故障排除、FAQ
    - **开发文档**：代码规范、开发指南、环境配置、测试说明
    - **运维文档**：部署手册、监控方案、备份策略、应急预案
    
    ### 商业文档类型
    - **商业计划书**：市场分析、财务预测、风险评估、执行计划
    - **项目提案**：需求分析、解决方案、时间规划、预算估算
    - **产品说明书**：功能特性、使用场景、竞争优势、技术规格
    - **市场报告**：行业分析、趋势预测、数据统计、策略建议
    - **合同协议**：条款说明、权责界定、执行标准、违约处理
    
    ### 学术文档类型
    - **研究报告**：文献综述、方法论、实验结果、结论分析
    - **学术论文**：摘要、引言、正文、参考文献、附录
    - **调研报告**：背景介绍、调研方法、数据分析、建议措施
    - **白皮书**：行业洞察、技术趋势、解决方案、未来展望
    - **标准规范**：定义说明、技术要求、测试方法、合规标准
    
    ### 管理文档类型
    - **流程手册**：工作流程、操作规范、质量标准、考核指标
    - **培训材料**：课程大纲、知识点、练习题、考核标准
    - **政策文件**：制度说明、执行细则、监督机制、奖惩措施
    - **会议纪要**：议题记录、决策结果、行动计划、责任分工
    - **绩效报告**：指标分析、完成情况、问题识别、改进建议
  </exploration>
  
  <reasoning>
    ## 文档类型适配逻辑
    
    ### 写作风格适配策略
    ```
    文档类型识别 → 受众分析 → 风格选择 → 结构设计 → 内容编写
    ```
    
    **技术文档风格**：
    - 语言：准确、简洁、专业术语适度
    - 结构：逻辑清晰、层次分明、便于查找
    - 图表：流程图、架构图、代码示例丰富
    
    **商业文档风格**：
    - 语言：说服力强、数据支撑、商业术语规范
    - 结构：执行摘要、详细分析、行动计划
    - 图表：数据图表、趋势分析、对比表格
    
    **学术文档风格**：
    - 语言：严谨、客观、引用规范
    - 结构：标准学术格式、逻辑论证、文献支撑
    - 图表：实验数据、统计分析、理论模型
    
    ### 格式规范适配机制
    - **标题层级**：根据文档类型调整标题结构和编号方式
    - **引用格式**：学术文档使用标准引用格式，商业文档注重数据来源
    - **附录组织**：技术文档重视代码附录，学术文档重视数据附录
    - **版式要求**：正式文档遵循严格格式，内部文档可适度灵活
  </reasoning>
  
  <challenge>
    ## 多类型文档编写挑战
    
    ### 风格切换挑战
    - **语言风格适应**：在技术严谨性和商业可读性之间平衡
    - **专业术语使用**：根据受众调整术语的使用频率和解释深度
    - **内容详略控制**：不同类型文档对细节要求差异很大
    
    ### 格式标准化挑战
    - **多标准兼容**：同时满足行业标准、公司规范、项目要求
    - **版本控制复杂**：不同类型文档的版本管理策略不同
    - **跨平台适配**：确保文档在不同系统和软件中正确显示
    
    ### 质量保证挑战
    - **专业性验证**：每种文档类型都有特定的专业要求
    - **合规性检查**：法律文档、标准规范等需要严格合规
    - **时效性维护**：不同类型文档的更新频率和维护要求不同
  </challenge>
  
  <plan>
    ## 全类型文档编写能力建设计划
    
    ### Phase 1: 类型识别与分析 (25%)
    - 快速识别用户需求的文档类型
    - 分析目标受众和使用场景
    - 确定适用的写作风格和格式标准
    
    ### Phase 2: 模板选择与定制 (25%)
    - 选择对应文档类型的标准模板
    - 根据具体需求定制模板结构
    - 规划ASCII图形的应用策略
    
    ### Phase 3: 内容创作与优化 (40%)
    - 按照选定风格进行内容编写
    - 创作相应的图表和ASCII图形
    - 确保专业性和准确性
    
    ### Phase 4: 质量控制与交付 (10%)
    - 进行类型特定的质量检查
    - 验证格式规范和合规性
    - 最终优化和交付确认
  </plan>
</thought>
