<?xml version="1.0" encoding="UTF-8"?>
<memory>
  <item id="mem_1754041427153_af0nn4ys5" time="2025/08/01 17:43">
    <content>
      AI小说助手项目核心开发指令：
      1. 仔细认真查看提供的文档资料以及信息
      2. 严格按照开发文档进行项目开发
      3. 测试后清理所有测试文件、脚本、页面
      4. 禁止创建多个文件，需修改时在原文件上进行
      5. UI风格、界面布局、图标、主题按开发文档执行
      6. 每个开发阶段完成后必须测试合格才能进入下一阶段
      7. 界面UI设计严格按文档的详细设计规范
      8. 禁止简化任何文件、页面及功能
      9. 自动激活对应专家进行专业开发
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754043317966_utz00zv9k" time="2025/08/01 18:15">
    <content>
      AI小说助手完整开发文档已深度分析：
      - 项目概述：PySide6+Python桌面应用，集成多AI模型，支持网络小说全流程创作
      - 技术架构：Material Design风格，SQLite+JSON存储，ChromaDB/Faiss向量库
      - 核心功能：大纲生成、大纲编辑、章节编辑、章节生成、章节分析、人物编辑、人物关系图、统计信息、AI聊天、提示词库、上下文管理、向量库检索、设置
      - 界面布局：左右40:60比例，明亮主题，禁紫色系，SVG图标，窗口记忆功能
      - 特殊要求：智能API检测、降AI味、中文日志、内置依赖、真实数据
    </content>
    <tags>#流程管理</tags>
  </item>
  <item id="mem_1754047093717_bczsq38bd" time="2025/08/01 19:18">
    <content>
      AI小说助手详细开发路线文档已创建完成：
      - 包含完整的项目概述、技术架构、项目结构
      - 详细的7个开发阶段规划：基础架构、AI集成、核心功能、高级功能、用户体验优化、质量保证、打包部署
      - 完整的界面布局设计：首页仪表盘、大纲生成、大纲编辑、章节编辑等所有模块
      - 核心功能详细规范：AI模型集成、提示词系统、降AI味功能等
      - 技术实现关键点：窗口记忆、智能API检测、降AI味算法
      - 质量控制标准和开发里程碑
      - 严格按照开发文档要求，涵盖全部功能内容
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754047268677_a815wyxz0" time="2025/08/01 19:21">
    <content>
      AI小说助手开发路线文档完整性检查完成：
      - 对比原始开发文档和开发计划5，进行了全面的功能完整性检查
      - 确认13个核心功能模块全部包含：大纲生成、大纲编辑、章节编辑、章节生成、章节分析、人物编辑、人物关系图、统计信息、AI聊天、提示词库、上下文管理、向量库检索、设置
      - 确认10个特殊功能要求全部包含：智能API检测、统一API管理、降AI味、智能检索、窗口记忆、中文日志、Material Design、SVG图标、40:60布局、真实数据
      - 确认7个技术要求全部包含：PySide6+Python、内置依赖、非企业级、PyInstaller打包、多AI模型、SQLite+JSON、向量库
      - 总计41项功能要求全部包含，无任何遗漏
    </content>
    <tags>#其他</tags>
  </item>
  <item id="mem_1754047453732_58qwqu0dy" time="2025/08/01 19:24">
    <content>
      系统总监项目开发总指挥职责确认：
      - 实时监控、协调、协作项目开发工作和进度
      - 确保每个阶段/子阶段自动激活相应专家进行开发
      - 确保每个阶段/子阶段全部完成后才能开始下一阶段
      - 严格以《AI小说助手详细开发路线文档.md》为主要执行依据
      - 以《AI小说助手开发文档.md》为辅助参考文档
      - 严格围绕两个文档执行，不能擅自修改或偏离
      - 建立完整的阶段门控机制和质量检查点
      - 实施专家角色智能调度和任务分配
    </content>
    <tags>#其他</tags>
  </item>
</memory>