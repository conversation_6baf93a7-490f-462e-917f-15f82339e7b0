<execution>
  <constraint>
    ## AI模型集成客观限制
    - **API调用频率限制**：各模型厂商的QPS和并发数限制
    - **上下文长度限制**：不同模型的最大token数限制差异
    - **网络延迟影响**：跨地域API调用的网络延迟不可控
    - **成本控制要求**：API调用成本需要在预算范围内
    - **模型能力差异**：不同模型在特定任务上的能力差异
  </constraint>

  <rule>
    ## 强制集成规则
    - **统一接口强制**：所有AI模型必须通过统一接口调用
    - **错误处理强制**：每个API调用都必须有完整的错误处理
    - **安全传输强制**：所有API调用必须使用HTTPS加密传输
    - **日志记录强制**：所有API调用都必须记录详细日志
    - **超时控制强制**：所有API调用都必须设置合理的超时时间
    - **重试机制强制**：临时性错误必须实现自动重试机制
  </rule>

  <guideline>
    ## 集成指导原则
    - **用户体验优先**：优化API调用速度，提升用户体验
    - **稳定性第一**：确保服务的高可用性和稳定性
    - **成本效益平衡**：在保证质量的前提下控制调用成本
    - **安全性保障**：严格保护用户数据和API密钥安全
    - **可扩展设计**：为未来新模型接入预留扩展能力
    - **监控可观测**：提供全面的监控和诊断能力
  </guideline>

  <process>
    ## AI模型集成标准流程
    
    ### Step 1: 统一接口架构设计
    ```mermaid
    flowchart TD
        A[需求分析] --> B[接口抽象设计]
        B --> C[适配器模式设计]
        C --> D[配置管理设计]
        D --> E[错误处理设计]
        E --> F[接口文档编写]
    ```
    
    **统一接口设计标准**：
    ```python
    class AIModelInterface:
        """AI模型统一接口"""
        
        async def generate_text(
            self,
            prompt: str,
            max_tokens: int = 1000,
            temperature: float = 0.7,
            **kwargs
        ) -> AIResponse:
            """文本生成统一接口"""
            pass
            
        async def chat_completion(
            self,
            messages: List[Dict],
            **kwargs
        ) -> AIResponse:
            """对话完成统一接口"""
            pass
    ```
    
    ### Step 2: 模型适配器开发
    ```mermaid
    graph TD
        A[OpenAI适配器] --> E[统一接口]
        B[Claude适配器] --> E
        C[Gemini适配器] --> E
        D[ModelScope适配器] --> E
        F[SiliconFlow适配器] --> E
        G[Ollama适配器] --> E
    ```
    
    **适配器实现标准**：
    ```python
    class OpenAIAdapter(AIModelInterface):
        """OpenAI模型适配器"""
        
        def __init__(self, api_key: str, model: str, base_url: str = None):
            self.client = OpenAI(api_key=api_key, base_url=base_url)
            self.model = model
            
        async def generate_text(self, prompt: str, **kwargs) -> AIResponse:
            try:
                response = await self.client.completions.create(
                    model=self.model,
                    prompt=prompt,
                    **self._convert_params(kwargs)
                )
                return self._convert_response(response)
            except Exception as e:
                return self._handle_error(e)
    ```
    
    ### Step 3: 配置管理系统
    ```mermaid
    flowchart LR
        A[配置文件] --> B[配置解析器]
        B --> C[模型工厂]
        C --> D[适配器实例]
        D --> E[统一接口调用]
    ```
    
    **配置管理标准**：
    ```python
    # config.yaml
    ai_models:
      openai:
        api_key: "${OPENAI_API_KEY}"
        model: "gpt-4-turbo"
        base_url: "https://api.openai.com/v1"
        max_retries: 3
        timeout: 30
      
      claude:
        api_key: "${CLAUDE_API_KEY}"
        model: "claude-3-opus-20240229"
        base_url: "https://api.anthropic.com"
        max_retries: 3
        timeout: 30
    ```
    
    ### Step 4: 错误处理和重试机制
    ```mermaid
    flowchart TD
        A[API调用] --> B{调用成功?}
        B -->|是| C[返回结果]
        B -->|否| D[错误分类]
        D --> E{可重试?}
        E -->|是| F[指数退避重试]
        E -->|否| G[返回错误]
        F --> H{重试次数超限?}
        H -->|否| A
        H -->|是| I[降级处理]
    ```
    
    **错误处理标准**：
    ```python
    class AIErrorHandler:
        """AI模型错误处理器"""
        
        RETRYABLE_ERRORS = [
            'rate_limit_exceeded',
            'server_error',
            'timeout',
            'connection_error'
        ]
        
        async def handle_error(self, error: Exception, context: dict) -> AIResponse:
            error_type = self._classify_error(error)
            
            if error_type in self.RETRYABLE_ERRORS:
                return await self._retry_with_backoff(context)
            else:
                return self._create_error_response(error)
    ```
    
    ### Step 5: 监控和日志系统
    ```mermaid
    graph TD
        A[API调用] --> B[性能监控]
        A --> C[错误监控]
        A --> D[成本监控]
        B --> E[监控仪表板]
        C --> E
        D --> E
        E --> F[告警系统]
    ```
    
    **监控指标标准**：
    ```python
    class AIModelMonitor:
        """AI模型监控器"""
        
        def record_api_call(
            self,
            model: str,
            operation: str,
            duration: float,
            tokens_used: int,
            success: bool,
            error_type: str = None
        ):
            # 记录API调用指标
            self.metrics.counter('api_calls_total').labels(
                model=model,
                operation=operation,
                success=success
            ).inc()
            
            self.metrics.histogram('api_call_duration').labels(
                model=model
            ).observe(duration)
    ```
  </process>

  <criteria>
    ## AI模型集成质量标准
    
    ### 功能完整性
    - ✅ 支持所有主要AI模型的集成
    - ✅ 统一接口覆盖所有核心功能
    - ✅ 配置管理系统功能完整
    - ✅ 错误处理机制覆盖全面
    
    ### 性能指标
    - ✅ API调用响应时间 ≤ 5秒（95%分位）
    - ✅ 并发处理能力 ≥ 100 QPS
    - ✅ 系统可用性 ≥ 99.9%
    - ✅ 错误率 ≤ 0.1%
    
    ### 可靠性指标
    - ✅ 自动重试成功率 ≥ 95%
    - ✅ 故障恢复时间 ≤ 30秒
    - ✅ 降级策略触发准确率 ≥ 99%
    - ✅ 数据一致性保证 100%
    
    ### 安全性指标
    - ✅ API密钥加密存储
    - ✅ 数据传输全程加密
    - ✅ 访问权限控制有效
    - ✅ 审计日志完整记录
    
    ### 可维护性
    - ✅ 代码覆盖率 ≥ 85%
    - ✅ 文档完整性 ≥ 90%
    - ✅ 监控指标覆盖率 ≥ 95%
    - ✅ 配置管理规范化
    
    ### 扩展性
    - ✅ 新模型接入时间 ≤ 2天
    - ✅ 接口向后兼容性保证
    - ✅ 配置热更新支持
    - ✅ 插件化架构支持
  </criteria>
</execution>
